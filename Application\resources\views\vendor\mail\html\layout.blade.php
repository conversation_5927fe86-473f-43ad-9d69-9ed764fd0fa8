<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="color-scheme" content="light">
    <meta name="supported-color-schemes" content="light">
    <style>
        body {
            color: {{ $settings['website_mail_normal_text_color'] }} !important;
        }
        .panel-content {
            background-color: {{ $settings['website_mail_background_color'] }} !important;
            color: #718096;

        }
        .panel-content p {
            color: {{ $settings['website_mail_normal_text_color'] }} !important;
        }
        .wrapper {
            background-color: {{ $settings['website_mail_background_color'] }} !important;
        }
        .body {
            background-color: {{ $settings['website_mail_background_color'] }} !important;
            border-bottom: 1px solid {{ $settings['website_mail_background_color'] }} !important;
            border-top: 1px solid {{ $settings['website_mail_background_color'] }} !important;
        }
        h1 {
            color: {{ $settings['website_mail_bold_text_color'] }} !important;
        }
        .header a {
            color: {{ $settings['website_mail_bold_text_color'] }} !important;
        }
        .button-blue,
        .button-primary {
            background-color: {{ $settings['website_mail_primary_color'] }} !important;
            border-bottom: 8px solid {{ $settings['website_mail_primary_color'] }} !important;
            border-left: 18px solid {{ $settings['website_mail_primary_color'] }} !important;
            border-right: 18px solid {{ $settings['website_mail_primary_color'] }} !important;
            border-top: 8px solid {{ $settings['website_mail_primary_color'] }} !important;
        }
        .message-box {
            border-left: 3px solid {{ $settings['website_mail_primary_color'] }};
        }
        .message-box.error {
            border-left: 3px solid #e53e3e;
        }
        @media only screen and (max-width: 600px) {
            .inner-body {
                width: 100% !important;
            }
            .footer {
                width: 100% !important;
            }
        }
        @media only screen and (max-width: 500px) {
            .button {
                width: 100% !important;
            }
        }
    </style>
</head>

<body>

    <table class="wrapper" width="100%" cellpadding="0" cellspacing="0" role="presentation">
        <tr>
            <td align="center">
                <table class="content" width="100%" cellpadding="0" cellspacing="0" role="presentation">
                    {{ $header ?? '' }}

                    <!-- Email Body -->
                    <tr>
                        <td class="body" width="100%" cellpadding="0" cellspacing="0">
                            <table class="inner-body" align="center" width="570" cellpadding="0" cellspacing="0"
                                role="presentation">
                                <!-- Body content -->
                                <tr>
                                    <td class="content-cell">
                                        {{ Illuminate\Mail\Markdown::parse($slot) }}

                                        {{ $subcopy ?? '' }}
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>

                    {{ $footer ?? '' }}
                </table>
            </td>
        </tr>
    </table>
</body>

</html>
