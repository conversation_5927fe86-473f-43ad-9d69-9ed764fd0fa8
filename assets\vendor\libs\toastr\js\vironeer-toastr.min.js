/* ==========================================================================
   Vironeer Toastr
   Version: 1.0.0
   Description: JavaScript for Vironeer Toastr notifications.
   ========================================================================== */
"use strict";const toastr={options:{},success:function(t,o){createToastr(t,o,this.options.colorsClasses.success,this.options.icons.success)},error:function(t,o){createToastr(t,o,this.options.colorsClasses.error,this.options.icons.error)},info:function(t,o){createToastr(t,o,this.options.colorsClasses.info,this.options.icons.info)},warning:function(t,o){createToastr(t,o,this.options.colorsClasses.warning,this.options.icons.warning)}},createToastr=(t,o,s,e)=>{var r;document.querySelector(".vironeer-toasts")||((r=document.createElement("div")).classList.add("vironeer-toasts"),r.classList.add(toastr.options.positionClass),document.body.appendChild(r));let n=document.createElement("div");if(toastr.options.preventDuplicates){let i=[],a=[];if(document.querySelectorAll(".vironeer-toast-title").forEach(t=>{i.push(t.textContent)}),document.querySelectorAll(".vironeer-toast-text").forEach(t=>{a.push(t.textContent)}),t&&o&&a.indexOf(t)>-1&&i.indexOf(o)>-1||a.indexOf(t)>-1)return!1}function c(){n.classList.add("close-animation"),setTimeout(()=>{n.remove()},toastr.options.animationDuration)}if(n.classList.add("vironeer-toast"),n.classList.add(s),n.style.setProperty("transition-duration",`${toastr.options.animationDuration}ms`),n.style.setProperty("animation-duration",`${toastr.options.animationDuration}ms`),n.innerHTML=` ${toastr.options.closeButton?`<button class="vironeer-toast-close ${toastr.options.closeButtonIcon}"></button>`:""} <div class="vironeer-toast-content"> ${toastr.options.showIcon?`<div class="vironeer-toast-icon ${e}"></div>`:""} <div class="vironeer-toast-context"> ${o?`<h6 class="vironeer-toast-title">${o}</h6>`:""} <p class="vironeer-toast-text">${t}</p> </div> </div> ${toastr.options.autoClose&&toastr.options.progressBar?'<div class="vironeer-toast-progress"></div>':""} `,document.querySelector(".vironeer-toasts").appendChild(n),toastr.options.autoClose){let l=toastr.options.duration,p,d;function u(){p=setInterval(()=>{let t=Math.round(100-(l-=10)/toastr.options.duration*100);toastr.options.progressBar&&n.querySelector(".vironeer-toast-progress").style.setProperty("width",`${t}%`),l<=0&&clearInterval(p)},10)}function v(){d=setTimeout(()=>{c()},l)}toastr.options.progressBar&&n.classList.add("vironeer-toast-auto-close"),u(),v(),n.addEventListener("mouseenter",()=>{clearTimeout(d),clearInterval(p)}),n.addEventListener("mouseleave",()=>{u(),v()})}toastr.options.closeButton&&n.querySelector("button").addEventListener("click",()=>{c()})};