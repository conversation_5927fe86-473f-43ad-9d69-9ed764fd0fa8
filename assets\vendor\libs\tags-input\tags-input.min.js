!function(t){"object"==typeof module&&"object"==typeof module.exports?module.exports=t():"object"==typeof window?window.TagsInput=t():console.error("To use this library you need to either use browser or node.js [require()]")}(function(){"use strict";var t=function(e){this.options=Object.assign(t.defaults,e),this.init()};function e(t){return/^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(t)}return t.prototype.init=function(t){if(this.options=t?Object.assign(this.options,t):this.options,this.initialized&&this.destroy(),!(this.orignal_input=document.getElementById(this.options.selector)))return console.error("tags-input couldn't find an element with the specified ID"),this;this.arr=[],this.wrapper=document.createElement("div");let i=this.input=document.createElement("input");var n;return(n=this).wrapper.append(n.input),n.wrapper.classList.add(n.options.wrapperClass),n.orignal_input.setAttribute("hidden","true"),n.orignal_input.parentNode.insertBefore(n.wrapper,n.orignal_input),function(t){t.wrapper.addEventListener("click",function(){t.input.focus()}),t.input.addEventListener("keydown",function(i){var n=t.input.value.trim();1==e(n)&&~[1,9,13,188].indexOf(i.keyCode)&&(i.preventDefault(),t.input.value="",""!=n&&t.addTag(n))}),t.input.addEventListener("focusout",function(i){var n=t.input.value.trim();1==e(n)&&(t.input.value="",""!=n&&t.addTag(n))})}(this),window.addEventListener("DOMSubtreeModified",()=>{0==document.querySelectorAll(".tags-input-wrapper .tag").length?i.placeholder=getUploadConfig.sendToTranslation:i.placeholder=""}),this.initialized=!0,this},t.prototype.addTag=function(t){if(!this.anyErrors(t)){this.arr.push(t);var e=this,i=document.createElement("span");i.className=this.options.tagClass,i.innerText=t;var n=document.createElement("a");return n.innerHTML="&times;",n.addEventListener("click",function(t){t.preventDefault();for(var i=this.parentNode,n=0;n<e.wrapper.childNodes.length;n++)e.wrapper.childNodes[n]==i&&e.deleteTag(i,n)}),i.appendChild(n),this.wrapper.insertBefore(i,this.input),this.orignal_input.value=this.arr.join(","),this}},t.prototype.deleteTag=function(t,e){return t.remove(),this.arr.splice(e,1),this.orignal_input.value=this.arr.join(","),this},t.prototype.anyErrors=function(t){return null!=this.options.max&&this.arr.length>=this.options.max?(console.log("max tags limit reached"),!0):!this.options.duplicate&&-1!=this.arr.indexOf(t)},t.prototype.addData=function(t){var e=this;return t.forEach(function(t){e.addTag(t)}),this},t.prototype.getInputString=function(){return this.arr.join(",")},t.prototype.destroy=function(){this.orignal_input.removeAttribute("hidden"),delete this.orignal_input;var t=this;Object.keys(this).forEach(function(e){t[e]instanceof HTMLElement&&t[e].remove(),"options"!=e&&delete t[e]}),this.initialized=!1},window.addEventListener("load",()=>{let t=document.querySelector(".tags-input-wrapper input");t.addEventListener("focusin",()=>{t.parentNode.classList.add("active")}),t.addEventListener("focusout",()=>{t.parentNode.classList.remove("active")})}),t.defaults={selector:"",wrapperClass:"tags-input-wrapper",tagClass:"tag",max:null,duplicate:!1},t});var tagInput1=new TagsInput({selector:"input-tags",duplicate:!1,max:10});