HOW TO ENABLE PHP EXTENSIONS IN XAMPP:

Method 1: Using XAMPP Control Panel
1. Open XAMPP Control Panel
2. Click "Config" button next to Apache
3. Select "PHP (php.ini)"
4. Find these lines and remove the semicolon (;) at the beginning:

   ;extension=gd
   ;extension=zip

   Change to:
   extension=gd
   extension=zip

5. Save the file
6. Restart Apache in XAMPP Control Panel

Method 2: Direct File Edit
1. Open C:\xampp\php\php.ini in Notepad
2. Search for "extension=gd" and "extension=zip"
3. Remove the semicolon (;) from the beginning of these lines
4. Save the file
5. Restart Apache

After enabling extensions:
- Restart Apache from XAMPP Control Panel
- Your Laravel application should work properly 