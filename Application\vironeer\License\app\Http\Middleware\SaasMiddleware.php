<?php

namespace Vironeer\License\App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class SaasMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if (!licenseType(2)) {
            return abort(404);
        }
        return $next($request);
    }
}
