/*! ========================================================================
 * Bootstrap Toggle: bootstrap-toggle.js v2.2.0
 * http://www.bootstraptoggle.com
 * ========================================================================
 * Copyright 2014 Min Hur, The New York Times Company
 * Licensed under MIT
 * ======================================================================== */
!function(n){"use strict";function i(t,e){this.$element=n(t),this.options=n.extend({},this.defaults(),e),this.render()}i.VERSION="2.2.0",i.DEFAULTS={on:"Active",off:"Disabled",onstyle:"green",offstyle:"red",size:"normal",style:"",width:"100%",height:null},i.prototype.defaults=function(){return{on:this.$element.attr("data-on")||i.DEFAULTS.on,off:this.$element.attr("data-off")||i.DEFAULTS.off,onstyle:this.$element.attr("data-onstyle")||i.DEFAULTS.onstyle,offstyle:this.$element.attr("data-offstyle")||i.DEFAULTS.offstyle,size:this.$element.attr("data-size")||i.DEFAULTS.size,style:this.$element.attr("data-style")||i.DEFAULTS.style,width:this.$element.attr("data-width")||i.DEFAULTS.width,height:this.$element.attr("data-height")||i.DEFAULTS.height}},i.prototype.render=function(){this._onstyle="btn-"+this.options.onstyle,this._offstyle="btn-"+this.options.offstyle;var t="large"===this.options.size?"btn-lg":"small"===this.options.size?"btn-sm":"mini"===this.options.size?"btn-xs":"",e=n('<label class="toggle-btn btn">').html(this.options.on).addClass(this._onstyle+" "+t),o=n('<label class="toggle-btn btn">').html(this.options.off).addClass(this._offstyle+" "+t+" active"),s=n('<span class="toggle-handle toggle-btn btn btn-dark">').addClass(t),i=n('<div class="toggle-group">').append(e,o,s),t=n('<div class="toggle btn" data-toggle="toggle">').addClass(this.$element.prop("checked")?this._onstyle:this._offstyle+" off").addClass(t).addClass(this.options.style);this.$element.wrap(t),n.extend(this,{$toggle:this.$element.parent(),$toggleOn:e,$toggleOff:o,$toggleGroup:i}),this.$toggle.append(i);i=this.options.width||Math.max(e.outerWidth(),o.outerWidth())+s.outerWidth()/2,s=this.options.height||Math.max(e.outerHeight(),o.outerHeight());e.addClass("toggle-on"),o.addClass("toggle-off"),this.$toggle.css({width:i,height:s}),this.options.height&&(e.css("line-height",e.height()+"px"),o.css("line-height",o.height()+"px")),this.update(!0),this.trigger(!0)},i.prototype.toggle=function(){this.$element.prop("checked")?this.off():this.on()},i.prototype.on=function(t){if(this.$element.prop("disabled"))return!1;this.$toggle.removeClass(this._offstyle+" off").addClass(this._onstyle),this.$element.prop("checked",!0),t||this.trigger()},i.prototype.off=function(t){if(this.$element.prop("disabled"))return!1;this.$toggle.removeClass(this._onstyle).addClass(this._offstyle+" off"),this.$element.prop("checked",!1),t||this.trigger()},i.prototype.enable=function(){this.$toggle.removeAttr("disabled"),this.$element.prop("disabled",!1)},i.prototype.disable=function(){this.$toggle.attr("disabled","disabled"),this.$element.prop("disabled",!0)},i.prototype.update=function(t){this.$element.prop("disabled")?this.disable():this.enable(),this.$element.prop("checked")?this.on(t):this.off(t)},i.prototype.trigger=function(t){this.$element.off("change.bs.toggle"),t||this.$element.change(),this.$element.on("change.bs.toggle",n.proxy(function(){this.update()},this))},i.prototype.destroy=function(){this.$element.off("change.bs.toggle"),this.$toggleGroup.remove(),this.$element.removeData("bs.toggle"),this.$element.unwrap()};var t=n.fn.bootstrapToggle;n.fn.bootstrapToggle=function(s){return this.each(function(){var t=n(this),e=t.data("bs.toggle"),o="object"==typeof s&&s;e||t.data("bs.toggle",e=new i(this,o)),"string"==typeof s&&e[s]&&e[s]()})},n.fn.bootstrapToggle.Constructor=i,n.fn.toggle.noConflict=function(){return n.fn.bootstrapToggle=t,this},n(function(){n("input[type=checkbox][data-toggle^=toggle]").bootstrapToggle()}),n(document).on("click.bs.toggle","div[data-toggle^=toggle]",function(t){n(this).find("input[type=checkbox]").bootstrapToggle("toggle"),t.preventDefault()})}(jQuery);