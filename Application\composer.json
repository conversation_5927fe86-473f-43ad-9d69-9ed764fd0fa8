{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.1", "anhskohbo/no-captcha": "^3.5", "bacon/bacon-qr-code": "^2.0", "cviebrock/eloquent-sluggable": "^10.0", "guzzlehttp/guzzle": "^7.2", "hashids/hashids": "^5.0", "intervention/image": "^2.7", "jenssegers/date": "^4.0", "laravel/framework": "^10.10", "laravel/sanctum": "^3.2", "laravel/socialite": "^5.5", "laravel/tinker": "^2.8", "laravel/ui": "^4.2.2", "league/flysystem-aws-s3-v3": "^3.0", "mcamara/laravel-localization": "^1.8", "mollie/laravel-mollie": "^2.0", "pion/laravel-chunk-upload": "^1.5", "pragmarx/google2fa-laravel": "^2.0", "stechstudio/laravel-zipstream": "^4.7", "stripe/stripe-php": "^8.11", "vironeer/toastr": "^1.0"}, "require-dev": {"spatie/laravel-ignition": "^2.0", "fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.1"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Vironeer\\": "vironeer/", "Vironeer\\License\\": "vironeer/License/", "Vironeer\\License\\App\\": "vironeer/License/app", "Vironeer\\Addons\\": "addons/src/", "Vironeer\\Addons\\App\\": "addons/src/app/", "Vironeer\\PayPal\\": "vironeer/Paypal/"}, "files": ["app/Http/Helpers/Helper.php", "app/Http/Helpers/WidgetHelper.php", "app/Http/Helpers/AdsHelper.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}