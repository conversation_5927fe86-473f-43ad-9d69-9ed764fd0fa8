/* ==========================================================================
   Vironeer Toastr
   Version: 1.0.0
   Description: Styles for Vironeer Toastr notifications.
   ========================================================================== */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@100;200;300;400;500;600;700;800;900&display=swap'); 
*, ::before, ::after { -webkit-box-sizing: border-box; box-sizing: border-box; } .vironeer-toasts { position: fixed; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -ms-flex-direction: column; flex-direction: column; font-family: -apple-system, BlinkMacSystemFont, 'Montserrat', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; top: 0; right: 0; padding: 10px; height: 0; width: 320px; z-index: 99999; } .vironeer-toast-bottom-right { top: auto; bottom: 0; -webkit-box-orient: vertical; -webkit-box-direction: reverse; -ms-flex-direction: column-reverse; flex-direction: column-reverse; } .vironeer-toast-top-left { top: 0; left: 0; right: auto; } .vironeer-toast-bottom-left { top: auto; right: auto; left: 0; bottom: 0; -webkit-box-orient: vertical; -webkit-box-direction: reverse; -ms-flex-direction: column-reverse; flex-direction: column-reverse; } .vironeer-toast-top-center { right: 50%; -webkit-transform: translateX(50%); -ms-transform: translateX(50%); transform: translateX(50%); } .vironeer-toast-bottom-center { top: auto; right: 50%; bottom: 0; -webkit-transform: translateX(50%); -ms-transform: translateX(50%); transform: translateX(50%); -webkit-box-orient: vertical; -webkit-box-direction: reverse; -ms-flex-direction: column-reverse; flex-direction: column-reverse; } .vironeer-toast { position: relative; width: 300px; right: 0; border-radius: 10px; -webkit-animation-name: toast; animation-name: toast; overflow: hidden; margin-bottom: 10px; -ms-flex-negative: 0; flex-shrink: 0; } .close-animation { right: -110%; } .vironeer-toast-bottom-right .vironeer-toast { margin-top: 10px; margin-bottom: 0; } .vironeer-toast-top-left .vironeer-toast, .vironeer-toast-bottom-left .vironeer-toast { right: auto; left: 0; -webkit-animation-name: toastLeft; animation-name: toastLeft; } .vironeer-toast-top-left .vironeer-toast.close-animation, .vironeer-toast-bottom-left .vironeer-toast.close-animation { right: auto; left: -110%; } .vironeer-toast-bottom-left .vironeer-toast { margin-top: 10px; margin-bottom: 0; } .vironeer-toast-top-center .vironeer-toast, .vironeer-toast-bottom-center .vironeer-toast { -webkit-animation: toastCenter; animation: toastCenter; } .vironeer-toast-top-center .close-animation, .vironeer-toast-bottom-center .close-animation { right: auto; -webkit-transform: scale(0); -ms-transform: scale(0); transform: scale(0); } .vironeer-toast-close { position: absolute; top: 10px; inset-inline-end: 5px; background: none; outline: none; border: 0; cursor: pointer; width: 21px; height: 18px; background-size: 18px 18px; background-position: center center; background-repeat: no-repeat; } .vironeer-toast-close-icon { background-image: url('data:image/svg+xml,<svg fill="%23fff" width="800px" height="800px" viewBox="-3.5 0 19 19" xmlns="http://www.w3.org/2000/svg" class="cf-icon-svg"><path d="M11.383 13.644A1.03 1.03 0 0 1 9.928 15.1L6 11.172 2.072 15.1a1.03 1.03 0 1 1-1.455-1.456l3.928-3.928L.617 5.79a1.03 1.03 0 1 1 1.455-1.456L6 8.261l3.928-3.928a1.03 1.03 0 0 1 1.455 1.456L7.455 9.716z"/></svg>'); } .vironeer-toast-content { display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; padding: 24px; color: #fff; } .vironeer-toast-auto-close .vironeer-toast-content { padding-bottom: 29px; } .vironeer-toast-icon { display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; width: 30px; height: 30px; border: 2px solid #fff; border-radius: 50%; background-size: 17px 17px; background-position: center center; background-repeat: no-repeat; -webkit-margin-end: 14px; margin-inline-end: 14px; -ms-flex-negative: 0; flex-shrink: 0; } .vironeer-toast-success-icon { background-image: url('data:image/svg+xml,<svg fill="%23fff" width="800px" height="800px" viewBox="0 0 36 36" version="1.1" preserveAspectRatio="xMidYMid meet" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>success-line</title><path class="clr-i-outline clr-i-outline-path-1" d="M13.72,27.69,3.29,17.27a1,1,0,0,1,1.41-1.41l9,9L31.29,7.29a1,1,0,0,1,1.41,1.41Z"></path><rect x="0" y="0" width="36" height="36" fill-opacity="0"/></svg>'); } .vironeer-toast-error-icon { background-image: url('data:image/svg+xml,<svg fill="%23fff" width="800px" height="800px" viewBox="-3.5 0 19 19" xmlns="http://www.w3.org/2000/svg" class="cf-icon-svg"><path d="M11.383 13.644A1.03 1.03 0 0 1 9.928 15.1L6 11.172 2.072 15.1a1.03 1.03 0 1 1-1.455-1.456l3.928-3.928L.617 5.79a1.03 1.03 0 1 1 1.455-1.456L6 8.261l3.928-3.928a1.03 1.03 0 0 1 1.455 1.456L7.455 9.716z"/></svg>'); } .vironeer-toast-info-icon { background-image: url('data:image/svg+xml,<svg width="800px" height="800px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.6 3H10V6.6H13.6V3ZM13.6 10.2H10V21H13.6V10.2Z" fill="%23fff"/></svg>'); } .vironeer-toast-warning-icon { background-image: url('data:image/svg+xml,<svg fill="%23fff" width="800px" height="800px" viewBox="0 0 1920 1920" xmlns="http://www.w3.org/2000/svg"><path d="M933.974 1477.394c-122.027 0-221.303 99.276-221.303 221.303S811.947 1920 933.974 1920s221.303-99.276 221.303-221.303-99.276-221.303-221.303-221.303zM1227.948 0H640l143.188 1298.171h301.572z" fill-rule="evenodd"/></svg>'); } .vironeer-toast-title { font-size: 16px; margin: 0; margin-bottom: 8px; font-weight: 600; } .vironeer-toast-text { margin-block: 0; word-break: break-word; } .vironeer-toast-progress { position: absolute; bottom: 0; height: 5px; width: 0; -webkit-transition-timing-function: linear; -o-transition-timing-function: linear; transition-timing-function: linear; background-color: rgba(255, 255, 255, 0.5); border-radius: 2px; -webkit-transition: linear 10ms; -o-transition: linear 10ms; transition: linear 10ms; } .vironeer-toast-success { background-color: #8bc34a; } .vironeer-toast-error { background-color: #f44336; } .vironeer-toast-info { background-color: #00bcd4; } .vironeer-toast-warning { background-color: #f6b800; } @-webkit-keyframes toast { 0% { right: -110%; } 100% { right: 0; } } @keyframes toast { 0% { right: -100%; } 100% { right: 0; } } @-webkit-keyframes toastLeft { 0% { left: -110%; } 100% { left: 0; } } @keyframes toastLeft { 0% { left: -100%; } 100% { left: 0; } } @-webkit-keyframes toastCenter { 0% { -webkit-transform: scale(0); transform: scale(0); } 100% { -webkit-transform: scale(1); transform: scale(1); } } @keyframes toastCenter { 0% { -webkit-transform: scale(0); transform: scale(0); } 100% { -webkit-transform: scale(1); transform: scale(1); } }