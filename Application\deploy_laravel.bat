@echo off
REM Laravel Deployment Script for XAMPP

REM 1. Ensure you are in the Application directory
cd /d C:\xampp\htdocs\File-transfer\Application

REM 2. Composer install (production)
composer install --optimize-autoloader --no-dev

REM 3. Generate Laravel app key (if not already set)
php artisan key:generate --force

REM 4. Create missing views directory for addons (if needed)
if not exist "addons\src\resources\views" (
    mkdir "addons\src\resources\views"
    echo Created missing directory: addons\src\resources\views
)

REM 5. Clear and cache config, routes, and views
php artisan config:clear
php artisan config:cache
php artisan route:clear
php artisan route:cache
php artisan view:clear
php artisan view:cache

echo.
echo Deployment steps completed!
pause 