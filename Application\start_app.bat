@echo off
REM Laravel/XAMPP App Startup Script
REM This will start Apache and MySQL, then open your app in the browser

REM Start Apache
start "" "C:\xampp\apache_start.bat"

REM Start MySQL
start "" "C:\xampp\mysql_start.bat"

REM Wait a few seconds for services to start
ping 127.0.0.1 -n 6 > nul

REM Open the app in the default browser
start "" "http://localhost/File-transfer/Application/public"

echo.
echo App is starting! If you see your browser open, everything is working.
pause 