@extends('frontend.layouts.front')
@section('title', $SeoConfiguration->title ?? '')
@section('content')
    <div class="landing-hero-bg"></div>
    <div class="header-content">
        <div class="container-lg">
            <div class="row align-items-center justify-content-center flex-column-reverse flex-lg-row" style="min-height: 70vh;">
                <!-- Left: Headline, subheadline, icon, button -->
                <div class="col-12 col-lg-6 text-center text-lg-start mb-5 mb-lg-0 landing-hero-anim">
                    <div class="header-content-icon mb-4" data-aos="fade" data-aos-duration="1000" data-dz-click>
                        <i class="fa-solid fa-paper-plane"></i>
                    </div>
                    <h2 class="mb-4 text-white fw-bold" data-aos="fade-right" data-aos-duration="1000" data-aos-delay="250">
                        {{ lang('Free Unlimited Files Storage', 'home page') }}
                    </h2>
                    <p class="lead text-white mb-4" data-aos="fade-left" data-aos-duration="1000" data-aos-delay="500">
                        {{ lang('Transfer your files and have them travel around the world for free, easily and securely.', 'home page') }}
                        </p>
                    <div data-aos="fade-up" data-aos-duration="1000" data-aos-delay="750">
                        @if (subscription()->is_subscribed)
                            <button class="btn btn-secondary btn-md" data-dz-click>
                                <i class="fa-solid fa-paper-plane me-2"></i>
                                {{ lang('Start Transfer', 'home page') }}
                            </button>
                        @else
                            <a href="{{ route('login') }}" class="btn btn-secondary btn-md">
                                <i class="fas fa-sign-in-alt me-2"></i>{{ lang('Get Started', 'home page') }}
                            </a>
                        @endif
                    </div>
                </div>
                <!-- Right: Uploader (dropzone-wrapper) -->
                <div class="col-12 col-lg-6 d-flex justify-content-center align-items-center landing-hero-anim-right">
                @if (subscription()->is_subscribed)
                        <div id="dropzone-wrapper" class="dropzone-wrapper landing-uploader-card w-100" style="max-width: 420px;">
                            <div class="upload-card-modern text-center p-4">
                                <div id="uploadCardDropzone">
                                    @include('frontend.home_upload_partial')
                                </div>
                            </div>
                        </div>
                    @else
                        <div class="landing-uploader-card-guest text-center d-flex flex-column align-items-center justify-content-center" style="min-height:340px;max-width:420px;width:100%;">
                            <div class="mb-3" style="font-size:2.8rem;color:var(--secondaryColor);">
                                <i class="fa-solid fa-cloud-arrow-up"></i>
                            </div>
                            <h5 class="mb-2 text-white fw-bold">Start transferring files</h5>
                            <p class="mb-4 text-white-50">Sign up or log in to upload and share your files securely.</p>
                            <div class="d-flex gap-2 justify-content-center">
                                <a href="{{ route('register') }}" class="btn btn-secondary btn-sm px-4">Sign Up</a>
                                <a href="{{ route('login') }}" class="btn btn-outline-light btn-sm px-4">Sign In</a>
                        </div>
                    </div>
                @endif
                </div>
            </div>
        </div>
    </div>
    @if (subscription()->is_subscribed)
        @push('config')
            @php
                $maxTransferSizeError = str_replace('{maxTransferSize}', subscription()->plan->transfer_size ?? 0, lang('Max size per transfer : {maxTransferSize}.', 'upload zone'));
                $userRemainingStorageSpace = subscription()->is_subscribed ? subscription()->storage->remaining_space_number : 0;
                $maxTransferSize = subscription()->plan->transfer_size_number;
                $subscribed = subscription()->is_subscribed ? 1 : 0;
                $subscriptionExpired = subscription()->is_expired ? 1 : 0;
                $subscriptionCanceled = subscription()->is_canceled ? 1 : 0;
                $unsubscribedError = !is_null(subscription()->plan->id) ? lang('You have no subscription or your subscription has been expired', 'alerts') : lang('Login or create account to start transferring files', 'alerts');
                $subscriptionCanceledError = lang('Your subscription has been canceled, please contact us for more information', 'alerts');
                $transferPassword = subscription()->plan->transfer_password ? 1 : 0;
                $transferNotify = subscription()->plan->transfer_notify ? 1 : 0;
                $transferExpiry = subscription()->plan->transfer_expiry ? 1 : 0;
            @endphp
            <script>
                "use strict";
                const uploadConfig = {
                    sizesTranslation: ["{{ lang('bytes') }}", "{{ lang('KB') }}", "{{ lang('MB') }}",
                        "{{ lang('GB') }}", "{{ lang('TB') }}"
                    ],
                    sendToTranslation: "{{ lang('Send to', 'upload zone') }}",
                    subscribed: "{{ $subscribed }}",
                    subscriptionExpired: "{{ $subscriptionExpired }}",
                    subscriptionCanceled: "{{ $subscriptionCanceled }}",
                    subscriptionCanceledError: "{{ $subscriptionCanceledError }}",
                    unsubscribedError: "{{ $unsubscribedError }}",
                    userRemainingStorageSpace: "{{ $userRemainingStorageSpace }}",
                    insufficientStorageSpaceError: "{{ lang('Insufficient storage space, please check your space or upgrade your plan', 'alerts') }}",
                    maxTransferSize: "{{ $maxTransferSize }}",
                    maxTransferSizeError: "{{ $maxTransferSizeError }}",
                    transferPassword: "{{ $transferPassword }}",
                    transferNotify: "{{ $transferNotify }}",
                    transferExpiry: "{{ $transferExpiry }}",
                };
                let stringifyUploadConfig = JSON.stringify(uploadConfig),
                    getUploadConfig = JSON.parse(stringifyUploadConfig);
            </script>
            @include('frontend.includes.dropzone-options')
        @endpush
        @push('scripts_libs')
            <script src="{{ asset('assets/vendor/libs/dropzone/dropzone.min.js') }}"></script>
            <script src="{{ asset('assets/vendor/libs/tags-input/tags-input.min.js') }}"></script>
            <script src="{{ asset('assets/vendor/libs/autosize/autosize.min.js') }}"></script>
        @endpush
        @push('scripts')
            <script src="{{ asset('assets/js/handler.js') }}"></script>
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    var dropzone = document.querySelector('#dropzone');
                    var intro = document.getElementById('uploadCardIntro');
                    var dropzoneSection = document.getElementById('uploadCardDropzone');

                    function toggleIntro() {
                        if (dropzone && intro && dropzoneSection) {
                            // Check if files are uploaded (has file previews)
                            var filePreviewElements = dropzone.querySelectorAll('.dz-preview');
                            var hasFiles = filePreviewElements.length > 0;

                            if (hasFiles) {
                                // Hide intro elements when files are present
                                intro.style.display = 'none';
                                dropzoneSection.style.display = 'block';

                                // Also hide the upload prompt elements specifically
                                var uploadIcon = document.querySelector('.upload-card-modern .header-content-icon');
                                var uploadTitle = document.querySelector('.upload-card-modern .upload-card-title');
                                var uploadText = document.querySelector('.upload-card-modern .upload-card-text');
                                var startTransferBtn = document.querySelector('.upload-card-modern .btn-start-transfer');

                                if (uploadIcon) uploadIcon.style.display = 'none';
                                if (uploadTitle) uploadTitle.style.display = 'none';
                                if (uploadText) uploadText.style.display = 'none';
                                if (startTransferBtn) startTransferBtn.style.display = 'none';
                            } else {
                                // Show intro elements when no files
                                intro.style.display = 'block';
                                dropzoneSection.style.display = 'block';

                                // Show the upload prompt elements
                                var uploadIcon = document.querySelector('.upload-card-modern .header-content-icon');
                                var uploadTitle = document.querySelector('.upload-card-modern .upload-card-title');
                                var uploadText = document.querySelector('.upload-card-modern .upload-card-text');
                                var startTransferBtn = document.querySelector('.upload-card-modern .btn-start-transfer');

                                if (uploadIcon) uploadIcon.style.display = 'block';
                                if (uploadTitle) uploadTitle.style.display = 'block';
                                if (uploadText) uploadText.style.display = 'block';
                                if (startTransferBtn) startTransferBtn.style.display = 'block';
                            }
                        }
                    }

                    if (dropzone) {
                        // Use MutationObserver for better file detection
                        var observer = new MutationObserver(function(mutations) {
                            mutations.forEach(function(mutation) {
                                if (mutation.type === 'childList') {
                                    toggleIntro();
                                }
                            });
                        });

                        observer.observe(dropzone, {
                            childList: true,
                            subtree: true
                        });

                        // Initial check
                        toggleIntro();

                        // Also listen to dropzone events for better reliability
                        if (window.Dropzone && window.Dropzone.instances.length > 0) {
                            var dzInstance = window.Dropzone.instances[0];
                            dzInstance.on('addedfile', toggleIntro);
                            dzInstance.on('removedfile', toggleIntro);
                            dzInstance.on('reset', toggleIntro);
                        }
                    }
                });
            </script>
        @endpush
    @endif
@endsection
