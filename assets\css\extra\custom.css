/*
| Enhanced UI Design for Right Side Upload Section
| Modern, beautiful, and user-friendly improvements
*/

/* Enhanced Color Variables for Modern Design */
:root {
    /* Enhanced Primary Colors */
    --upload-card-bg: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 100%);
    --upload-card-border: rgba(99, 102, 241, 0.2);
    --upload-card-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(99, 102, 241, 0.05);
    --upload-card-shadow-hover: 0 32px 64px -12px rgba(0, 0, 0, 0.35), 0 0 0 1px rgba(99, 102, 241, 0.15);

    /* Modern Gradient Colors */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --success-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);

    /* Enhanced Text Colors */
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --text-accent: #a78bfa;

    /* Interactive Elements */
    --button-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    --button-primary-hover: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    --button-secondary: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);
    --button-secondary-hover: linear-gradient(135deg, #0891b2 0%, #2563eb 100%);

    /* Form Elements */
    --input-bg: rgba(30, 41, 59, 0.8);
    --input-border: rgba(99, 102, 241, 0.3);
    --input-border-focus: rgba(99, 102, 241, 0.6);
    --input-shadow-focus: 0 0 0 3px rgba(99, 102, 241, 0.1);

    /* File Preview Elements */
    --file-preview-bg: rgba(51, 65, 85, 0.9);
    --file-preview-border: rgba(99, 102, 241, 0.15);
    --file-preview-hover: rgba(71, 85, 105, 0.9);

    /* Animation Variables */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Enhanced Upload Card Modern Styling */
.upload-card-modern {
    background: var(--upload-card-bg) !important;
    border: 1px solid var(--upload-card-border) !important;
    border-radius: 24px !important;
    box-shadow: var(--upload-card-shadow) !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    padding: 32px 24px !important;
    position: relative !important;
    overflow: hidden !important;
    transition: var(--transition-smooth) !important;
}

.upload-card-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.5), transparent);
    opacity: 0.7;
}

.upload-card-modern:hover {
    box-shadow: var(--upload-card-shadow-hover) !important;
    transform: translateY(-2px) scale(1.01) !important;
    border-color: rgba(99, 102, 241, 0.4) !important;
}

/* Enhanced Cloud Upload Icon */
.upload-card-modern .fa-cloud-arrow-up {
    font-size: 3.5rem !important;
    background: var(--accent-gradient) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    margin-bottom: 16px !important;
    filter: drop-shadow(0 4px 8px rgba(79, 172, 254, 0.3));
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
}

/* Enhanced Typography */
.upload-card-modern h5 {
    font-size: 1.5rem !important;
    font-weight: 700 !important;
    color: var(--text-primary) !important;
    margin-bottom: 8px !important;
    letter-spacing: -0.025em !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.upload-card-modern p {
    color: var(--text-secondary) !important;
    font-size: 1.1rem !important;
    margin-bottom: 24px !important;
    line-height: 1.6 !important;
    font-weight: 400 !important;
}

/* Enhanced Primary Button */
.upload-card-modern .btn {
    background: var(--button-primary) !important;
    color: var(--text-primary) !important;
    border: none !important;
    border-radius: 16px !important;
    font-size: 1.125rem !important;
    font-weight: 600 !important;
    padding: 16px 32px !important;
    box-shadow: 0 8px 25px -8px rgba(99, 102, 241, 0.4) !important;
    transition: var(--transition-smooth) !important;
    position: relative !important;
    overflow: hidden !important;
    letter-spacing: 0.025em !important;
}

.upload-card-modern .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.upload-card-modern .btn:hover::before {
    left: 100%;
}

.upload-card-modern .btn:hover {
    background: var(--button-primary-hover) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 12px 35px -8px rgba(99, 102, 241, 0.6) !important;
}

.upload-card-modern .btn:active {
    transform: translateY(0px) !important;
    box-shadow: 0 4px 15px -8px rgba(99, 102, 241, 0.4) !important;
}

/* Enhanced Dropzone Upload Box */
.upload-card-modern .dropzone-uploadbox {
    background: linear-gradient(135deg, rgba(15, 23, 42, 0.98) 0%, rgba(30, 41, 59, 0.98) 100%) !important;
    border: 1px solid var(--upload-card-border) !important;
    border-radius: 20px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2) inset, 0 4px 20px rgba(99, 102, 241, 0.1) !important;
    padding: 28px 20px !important;
    margin: 0 auto 16px auto !important;
    max-width: 380px !important;
    width: 100% !important;
    transition: var(--transition-smooth) !important;
    position: relative !important;
}

.upload-card-modern .dropzone-uploadbox::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 20px;
    padding: 1px;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.3), rgba(168, 85, 247, 0.3));
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.upload-card-modern .dropzone-uploadbox:hover::before {
    opacity: 1;
}

/* Enhanced Dropzone Header */
.upload-card-modern .dropzone-uploadbox-header {
    margin-bottom: 16px !important;
    padding-bottom: 16px !important;
    border-bottom: 1px solid rgba(99, 102, 241, 0.1) !important;
}

.upload-card-modern .dropzone-more {
    padding: 12px 16px !important;
    border-radius: 12px !important;
    background: rgba(51, 65, 85, 0.6) !important;
    border: 1px solid rgba(99, 102, 241, 0.2) !important;
    transition: var(--transition-smooth) !important;
}

.upload-card-modern .dropzone-more:hover {
    background: rgba(71, 85, 105, 0.8) !important;
    border-color: rgba(99, 102, 241, 0.4) !important;
    transform: translateY(-1px) !important;
}

.upload-card-modern .dropzone-more i {
    font-size: 24px !important;
    background: var(--success-gradient) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
}

.upload-card-modern .dropzone-more-title {
    font-size: 1.125rem !important;
    font-weight: 600 !important;
    color: var(--text-primary) !important;
    margin-bottom: 4px !important;
}

.upload-card-modern .dropzone-more-text {
    color: var(--text-muted) !important;
    font-size: 0.875rem !important;
    font-weight: 400 !important;
}

.upload-card-modern .dropzone-reset {
    padding: 8px 12px !important;
    border-radius: 10px !important;
    background: rgba(239, 68, 68, 0.1) !important;
    border: 1px solid rgba(239, 68, 68, 0.3) !important;
    color: #fca5a5 !important;
    transition: var(--transition-smooth) !important;
}

.upload-card-modern .dropzone-reset:hover {
    background: rgba(239, 68, 68, 0.2) !important;
    border-color: rgba(239, 68, 68, 0.5) !important;
    color: #f87171 !important;
    transform: translateY(-1px) !important;
}

/* Enhanced File Preview Styling */
.upload-card-modern .dz-file-preview {
    background: var(--file-preview-bg) !important;
    border: 1px solid var(--file-preview-border) !important;
    border-radius: 14px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    padding: 14px 16px !important;
    margin-bottom: 12px !important;
    display: flex !important;
    align-items: center !important;
    transition: var(--transition-smooth) !important;
    position: relative !important;
    overflow: hidden !important;
}

.upload-card-modern .dz-file-preview::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--accent-gradient);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.upload-card-modern .dz-file-preview:hover {
    background: var(--file-preview-hover) !important;
    border-color: rgba(99, 102, 241, 0.3) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2) !important;
    transform: translateY(-1px) !important;
}

.upload-card-modern .dz-file-preview:hover::before {
    opacity: 1;
}

.upload-card-modern .dz-fileicon span {
    color: #4facfe !important;
    font-size: 1.75rem !important;
    margin-right: 12px !important;
}

.upload-card-modern .dz-filename,
.upload-card-modern .dz-size,
.upload-card-modern .dz-percent {
    color: var(--text-primary) !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
}

.upload-card-modern .dz-remove {
    color: #f87171 !important;
    background: rgba(239, 68, 68, 0.1) !important;
    border: 1px solid rgba(239, 68, 68, 0.3) !important;
    border-radius: 8px !important;
    padding: 6px 8px !important;
    font-size: 1rem !important;
    margin-left: 12px !important;
    cursor: pointer !important;
    transition: var(--transition-smooth) !important;
}

.upload-card-modern .dz-remove:hover {
    color: #ef4444 !important;
    background: rgba(239, 68, 68, 0.2) !important;
    border-color: rgba(239, 68, 68, 0.5) !important;
    transform: scale(1.05) !important;
}

/* Enhanced Form Action Buttons */
.upload-card-modern .dropzone-form-action {
    background: rgba(51, 65, 85, 0.8) !important;
    border: 1px solid rgba(99, 102, 241, 0.2) !important;
    border-radius: 12px !important;
    color: var(--text-secondary) !important;
    font-weight: 500 !important;
    padding: 14px 18px !important;
    margin-right: 10px !important;
    transition: var(--transition-smooth) !important;
    cursor: pointer !important;
    position: relative !important;
    overflow: hidden !important;
}

.upload-card-modern .dropzone-form-action::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
    transition: left 0.5s;
}

.upload-card-modern .dropzone-form-action:hover::before {
    left: 100%;
}

.upload-card-modern .dropzone-form-action.selected,
.upload-card-modern .dropzone-form-action:hover {
    background: var(--button-secondary) !important;
    color: var(--text-primary) !important;
    border-color: rgba(6, 182, 212, 0.5) !important;
    box-shadow: 0 4px 20px rgba(6, 182, 212, 0.3) !important;
    transform: translateY(-2px) !important;
}

.upload-card-modern .dropzone-form-action i {
    margin-right: 8px !important;
    font-size: 1.1rem !important;
}

/* Enhanced Transfer Button */
.upload-card-modern .dropzone-uploadbox .btn,
.upload-card-modern .dropzone-uploadbox .btn:disabled {
    background: var(--button-primary) !important;
    color: var(--text-primary) !important;
    border: none !important;
    border-radius: 14px !important;
    font-size: 1.125rem !important;
    font-weight: 600 !important;
    padding: 16px 24px !important;
    margin-top: 12px !important;
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3) !important;
    transition: var(--transition-smooth) !important;
    position: relative !important;
    overflow: hidden !important;
    letter-spacing: 0.025em !important;
}

.upload-card-modern .dropzone-uploadbox .btn:not(:disabled):hover {
    background: var(--button-primary-hover) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 12px 35px rgba(99, 102, 241, 0.4) !important;
}

.upload-card-modern .dropzone-uploadbox .btn:disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    background: rgba(99, 102, 241, 0.3) !important;
}

/* Enhanced Form Inputs */
.upload-card-modern .dropzone-uploadbox input,
.upload-card-modern .dropzone-uploadbox textarea {
    background: var(--input-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--input-border) !important;
    border-radius: 12px !important;
    padding: 12px 16px !important;
    font-size: 1rem !important;
    transition: var(--transition-smooth) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.upload-card-modern .dropzone-uploadbox input:focus,
.upload-card-modern .dropzone-uploadbox textarea:focus {
    border-color: var(--input-border-focus) !important;
    box-shadow: var(--input-shadow-focus) !important;
    outline: none !important;
    background: rgba(30, 41, 59, 0.9) !important;
}

.upload-card-modern .dropzone-uploadbox input::placeholder,
.upload-card-modern .dropzone-uploadbox textarea::placeholder {
    color: var(--text-muted) !important;
    opacity: 0.8 !important;
}

/* Enhanced Form Labels */
.upload-card-modern .form-label {
    color: var(--text-secondary) !important;
    font-weight: 500 !important;
    font-size: 0.95rem !important;
    margin-bottom: 8px !important;
    letter-spacing: 0.025em !important;
}

/* Enhanced Progress Bar */
.upload-card-modern .dz-progress {
    background: rgba(51, 65, 85, 0.8) !important;
    border-radius: 8px !important;
    height: 6px !important;
    margin-top: 8px !important;
    overflow: hidden !important;
}

.upload-card-modern .dz-upload {
    background: var(--accent-gradient) !important;
    border-radius: 8px !important;
    height: 100% !important;
    transition: width 0.3s ease !important;
}

/* Enhanced Success/Error States */
.upload-card-modern .dz-success-mark {
    color: #10b981 !important;
    font-size: 1.2rem !important;
}

.upload-card-modern .dz-error-mark {
    color: #ef4444 !important;
    font-size: 1.2rem !important;
}

.upload-card-modern .dz-error-message {
    color: #fca5a5 !important;
    font-size: 0.875rem !important;
    margin-top: 4px !important;
    padding: 8px 12px !important;
    background: rgba(239, 68, 68, 0.1) !important;
    border-radius: 8px !important;
    border: 1px solid rgba(239, 68, 68, 0.3) !important;
}

/* Enhanced Guest Upload Card */
.landing-uploader-card-guest {
    background: var(--upload-card-bg) !important;
    border: 1px solid var(--upload-card-border) !important;
    border-radius: 24px !important;
    box-shadow: var(--upload-card-shadow) !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    padding: 40px 32px !important;
    transition: var(--transition-smooth) !important;
    position: relative !important;
    overflow: hidden !important;
}

.landing-uploader-card-guest::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.5), transparent);
    opacity: 0.7;
}

.landing-uploader-card-guest:hover {
    box-shadow: var(--upload-card-shadow-hover) !important;
    transform: translateY(-2px) scale(1.01) !important;
    border-color: rgba(99, 102, 241, 0.4) !important;
}

.landing-uploader-card-guest .fa-cloud-arrow-up {
    font-size: 3.5rem !important;
    background: var(--accent-gradient) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    margin-bottom: 16px !important;
    filter: drop-shadow(0 4px 8px rgba(79, 172, 254, 0.3));
    animation: float 3s ease-in-out infinite;
}

.landing-uploader-card-guest h5 {
    font-size: 1.5rem !important;
    font-weight: 700 !important;
    color: var(--text-primary) !important;
    margin-bottom: 8px !important;
    letter-spacing: -0.025em !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.landing-uploader-card-guest p {
    color: var(--text-secondary) !important;
    font-size: 1.1rem !important;
    margin-bottom: 24px !important;
    line-height: 1.6 !important;
    font-weight: 400 !important;
}

.landing-uploader-card-guest .btn {
    border-radius: 12px !important;
    font-weight: 600 !important;
    padding: 12px 24px !important;
    transition: var(--transition-smooth) !important;
    position: relative !important;
    overflow: hidden !important;
    letter-spacing: 0.025em !important;
}

.landing-uploader-card-guest .btn-secondary {
    background: var(--button-primary) !important;
    border: none !important;
    box-shadow: 0 8px 25px -8px rgba(99, 102, 241, 0.4) !important;
}

.landing-uploader-card-guest .btn-secondary:hover {
    background: var(--button-primary-hover) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 12px 35px -8px rgba(99, 102, 241, 0.6) !important;
}

.landing-uploader-card-guest .btn-outline-light {
    background: rgba(248, 250, 252, 0.1) !important;
    border: 1px solid rgba(248, 250, 252, 0.3) !important;
    color: var(--text-primary) !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
}

.landing-uploader-card-guest .btn-outline-light:hover {
    background: rgba(248, 250, 252, 0.2) !important;
    border-color: rgba(248, 250, 252, 0.5) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

/* Enhanced Responsive Design */
@media (max-width: 991.98px) {
    .upload-card-modern {
        padding: 28px 20px !important;
        border-radius: 20px !important;
    }

    .upload-card-modern .fa-cloud-arrow-up {
        font-size: 3rem !important;
    }

    .upload-card-modern h5 {
        font-size: 1.35rem !important;
    }

    .upload-card-modern .dropzone-uploadbox {
        max-width: 100% !important;
        padding: 24px 16px !important;
    }

    .landing-uploader-card-guest {
        padding: 32px 24px !important;
        border-radius: 20px !important;
    }
}

@media (max-width: 767.98px) {
    .upload-card-modern {
        padding: 24px 16px !important;
        border-radius: 18px !important;
    }

    .upload-card-modern .fa-cloud-arrow-up {
        font-size: 2.5rem !important;
    }

    .upload-card-modern h5 {
        font-size: 1.25rem !important;
    }

    .upload-card-modern p {
        font-size: 1rem !important;
    }

    .upload-card-modern .btn {
        font-size: 1rem !important;
        padding: 14px 28px !important;
    }

    .upload-card-modern .dropzone-uploadbox {
        padding: 20px 14px !important;
        border-radius: 16px !important;
    }

    .landing-uploader-card-guest {
        padding: 28px 20px !important;
        border-radius: 18px !important;
    }

    .landing-uploader-card-guest .fa-cloud-arrow-up {
        font-size: 2.5rem !important;
    }
}

/* Enhanced Animation for Better UX */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.upload-card-modern,
.landing-uploader-card-guest {
    animation: slideInUp 0.6s ease-out;
}

/* Enhanced Focus States for Accessibility */
.upload-card-modern .btn:focus,
.landing-uploader-card-guest .btn:focus {
    outline: 2px solid rgba(99, 102, 241, 0.5) !important;
    outline-offset: 2px !important;
}

.upload-card-modern .dropzone-form-action:focus {
    outline: 2px solid rgba(6, 182, 212, 0.5) !important;
    outline-offset: 2px !important;
}

/* Enhanced Visual Hierarchy - Typography Scale */
.upload-card-modern .dropzone-form-title {
    font-size: 1.25rem !important;
    font-weight: 600 !important;
    color: var(--text-primary) !important;
    margin-bottom: 12px !important;
    letter-spacing: -0.025em !important;
    display: flex !important;
    align-items: center !important;
}

.upload-card-modern .dropzone-form-title i {
    margin-right: 10px !important;
    font-size: 1.1rem !important;
    background: var(--accent-gradient) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
}

/* Enhanced Spacing and Layout */
.upload-card-modern .dropzone-forms .dropzone-form {
    background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 100%) !important;
    border: 1px solid rgba(99, 102, 241, 0.2) !important;
    border-radius: 16px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
    margin-bottom: 16px !important;
    overflow: hidden !important;
    position: relative !important;
}

.upload-card-modern .dropzone-forms .dropzone-form::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--accent-gradient);
    opacity: 0.8;
}

.upload-card-modern .dropzone-form-header {
    padding: 20px 24px 16px 24px !important;
    border-bottom: 1px solid rgba(99, 102, 241, 0.15) !important;
    background: rgba(51, 65, 85, 0.3) !important;
}

.upload-card-modern .dropzone-form-body {
    padding: 20px 24px 24px 24px !important;
}

/* Enhanced Form Field Grouping */
.upload-card-modern .dropzone-form-body > div {
    margin-bottom: 20px !important;
}

.upload-card-modern .dropzone-form-body > div:last-child {
    margin-bottom: 0 !important;
}

/* Enhanced Required Field Indicators */
.upload-card-modern .form-label .red {
    color: #f87171 !important;
    font-weight: 700 !important;
    margin-left: 4px !important;
}

/* Enhanced Checkbox and Switch Styling */
.upload-card-modern .form-check-input {
    background-color: rgba(51, 65, 85, 0.8) !important;
    border: 1px solid rgba(99, 102, 241, 0.3) !important;
    border-radius: 6px !important;
    width: 1.25rem !important;
    height: 1.25rem !important;
    transition: var(--transition-smooth) !important;
}

.upload-card-modern .form-check-input:checked {
    background-color: #6366f1 !important;
    border-color: #6366f1 !important;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2) !important;
}

.upload-card-modern .form-check-label {
    color: var(--text-secondary) !important;
    font-weight: 500 !important;
    margin-left: 8px !important;
    cursor: pointer !important;
}

/* Enhanced Tags Input Styling */
.upload-card-modern .tags-input {
    background: var(--input-bg) !important;
    border: 1px solid var(--input-border) !important;
    border-radius: 12px !important;
    padding: 8px 12px !important;
    min-height: 48px !important;
    transition: var(--transition-smooth) !important;
}

.upload-card-modern .tags-input:focus-within {
    border-color: var(--input-border-focus) !important;
    box-shadow: var(--input-shadow-focus) !important;
}

.upload-card-modern .tags-input .tag {
    background: var(--button-secondary) !important;
    color: var(--text-primary) !important;
    border: none !important;
    border-radius: 8px !important;
    padding: 4px 8px !important;
    margin: 2px !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
}

/* Enhanced Password Input with Toggle */
.upload-card-modern .input-password {
    position: relative !important;
}

.upload-card-modern .input-password button {
    position: absolute !important;
    right: 12px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    background: none !important;
    border: none !important;
    color: var(--text-muted) !important;
    cursor: pointer !important;
    padding: 4px !important;
    border-radius: 4px !important;
    transition: var(--transition-smooth) !important;
}

.upload-card-modern .input-password button:hover {
    color: var(--text-secondary) !important;
    background: rgba(99, 102, 241, 0.1) !important;
}

/* Enhanced Date Input Styling */
.upload-card-modern input[type="datetime-local"] {
    background: var(--input-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--input-border) !important;
    border-radius: 12px !important;
    padding: 12px 16px !important;
    font-size: 1rem !important;
    transition: var(--transition-smooth) !important;
    color-scheme: dark !important;
}

.upload-card-modern input[type="datetime-local"]:focus {
    border-color: var(--input-border-focus) !important;
    box-shadow: var(--input-shadow-focus) !important;
    outline: none !important;
}

/* Enhanced Textarea Styling */
.upload-card-modern textarea {
    min-height: 100px !important;
    resize: vertical !important;
    font-family: inherit !important;
    line-height: 1.5 !important;
}

/* Enhanced Interactive Elements - Dropdown Menus */
.upload-card-modern .drop-down-menu {
    background: linear-gradient(135deg, rgba(15, 23, 42, 0.98) 0%, rgba(30, 41, 59, 0.98) 100%) !important;
    border: 1px solid rgba(99, 102, 241, 0.3) !important;
    border-radius: 12px !important;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3) !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    padding: 8px !important;
    margin-top: 8px !important;
}

.upload-card-modern .drop-down-item {
    color: var(--text-secondary) !important;
    padding: 12px 16px !important;
    border-radius: 8px !important;
    transition: var(--transition-smooth) !important;
    display: flex !important;
    align-items: center !important;
    font-weight: 500 !important;
}

.upload-card-modern .drop-down-item:hover {
    background: rgba(99, 102, 241, 0.15) !important;
    color: var(--text-primary) !important;
    transform: translateX(4px) !important;
}

.upload-card-modern .drop-down-item i {
    margin-right: 10px !important;
    font-size: 1rem !important;
    width: 16px !important;
    text-align: center !important;
}

.upload-card-modern .drop-down-btn {
    background: rgba(51, 65, 85, 0.6) !important;
    border: 1px solid rgba(99, 102, 241, 0.2) !important;
    border-radius: 8px !important;
    padding: 8px 10px !important;
    color: var(--text-muted) !important;
    transition: var(--transition-smooth) !important;
    cursor: pointer !important;
}

.upload-card-modern .drop-down-btn:hover {
    background: rgba(71, 85, 105, 0.8) !important;
    border-color: rgba(99, 102, 241, 0.4) !important;
    color: var(--text-secondary) !important;
    transform: scale(1.05) !important;
}

/* Enhanced Form Submit Buttons */
.upload-card-modern .dropzone-form-cancel {
    background: rgba(239, 68, 68, 0.1) !important;
    border: 1px solid rgba(239, 68, 68, 0.3) !important;
    color: #fca5a5 !important;
    border-radius: 12px !important;
    padding: 12px 24px !important;
    font-weight: 600 !important;
    transition: var(--transition-smooth) !important;
}

.upload-card-modern .dropzone-form-cancel:hover {
    background: rgba(239, 68, 68, 0.2) !important;
    border-color: rgba(239, 68, 68, 0.5) !important;
    color: #f87171 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3) !important;
}

.upload-card-modern .dropzone-form-validate {
    background: var(--button-primary) !important;
    border: none !important;
    color: var(--text-primary) !important;
    border-radius: 12px !important;
    padding: 12px 24px !important;
    font-weight: 600 !important;
    transition: var(--transition-smooth) !important;
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3) !important;
}

.upload-card-modern .dropzone-form-validate:hover {
    background: var(--button-primary-hover) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 12px 35px rgba(99, 102, 241, 0.4) !important;
}

/* Enhanced Loading States */
.upload-card-modern .btn.loading {
    position: relative !important;
    color: transparent !important;
}

.upload-card-modern .btn.loading::after {
    content: '' !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    width: 20px !important;
    height: 20px !important;
    margin: -10px 0 0 -10px !important;
    border: 2px solid transparent !important;
    border-top: 2px solid currentColor !important;
    border-radius: 50% !important;
    animation: spin 1s linear infinite !important;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Drag and Drop States */
.upload-card-modern.dragover {
    border-color: rgba(99, 102, 241, 0.6) !important;
    box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1), var(--upload-card-shadow-hover) !important;
    transform: scale(1.02) !important;
}

.upload-card-modern.dragover::before {
    opacity: 1 !important;
    background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.8), transparent) !important;
}

/* Enhanced File Type Icons */
.upload-card-modern .dz-fileicon span[dz-file-extension]::before {
    background: var(--accent-gradient) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    font-weight: 600 !important;
}

/* Enhanced Success Completion Card */
.transfer-completed-card {
    background: var(--upload-card-bg) !important;
    border: 1px solid rgba(16, 185, 129, 0.3) !important;
    border-radius: 24px !important;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(16, 185, 129, 0.1) !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
}

.transfer-completed-card .upload-complete-icon {
    font-size: 4rem !important;
    background: var(--success-gradient) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    margin-bottom: 20px !important;
    animation: successPulse 2s ease-in-out infinite;
}

@keyframes successPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.transfer-completed-card .upload-complete-title {
    font-size: 1.5rem !important;
    font-weight: 700 !important;
    color: var(--text-primary) !important;
    margin-bottom: 12px !important;
}

.transfer-completed-card .upload-complete-text {
    color: var(--text-secondary) !important;
    font-size: 1.1rem !important;
    line-height: 1.6 !important;
    margin-bottom: 24px !important;
}

/* Enhanced Copy Link Input */
.transfer-completed-card .form-button {
    position: relative !important;
    display: flex !important;
    align-items: center !important;
    margin-bottom: 20px !important;
}

.transfer-completed-card .transfer-link {
    background: var(--input-bg) !important;
    border: 1px solid rgba(16, 185, 129, 0.3) !important;
    color: var(--text-primary) !important;
    border-radius: 12px 0 0 12px !important;
    padding: 12px 16px !important;
    font-family: 'Monaco', 'Menlo', monospace !important;
    font-size: 0.9rem !important;
}

.transfer-completed-card .btn-copy {
    background: var(--success-gradient) !important;
    border: none !important;
    color: var(--text-primary) !important;
    border-radius: 0 12px 12px 0 !important;
    padding: 12px 16px !important;
    cursor: pointer !important;
    transition: var(--transition-smooth) !important;
}

.transfer-completed-card .btn-copy:hover {
    transform: scale(1.05) !important;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4) !important;
}

/* Modern Layout Improvements - Enhanced Spacing System */
.upload-card-modern * {
    box-sizing: border-box !important;
}

/* Enhanced Container Spacing */
.upload-card-modern .dropzone-uploadbox-upper {
    padding: 0 !important;
}

.upload-card-modern .dropzone-uploadbox-files {
    padding: 0 4px !important;
    max-height: 320px !important;
    overflow-y: auto !important;
    scrollbar-width: thin !important;
    scrollbar-color: rgba(99, 102, 241, 0.3) transparent !important;
}

.upload-card-modern .dropzone-uploadbox-files::-webkit-scrollbar {
    width: 6px !important;
}

.upload-card-modern .dropzone-uploadbox-files::-webkit-scrollbar-track {
    background: rgba(51, 65, 85, 0.3) !important;
    border-radius: 3px !important;
}

.upload-card-modern .dropzone-uploadbox-files::-webkit-scrollbar-thumb {
    background: rgba(99, 102, 241, 0.4) !important;
    border-radius: 3px !important;
}

.upload-card-modern .dropzone-uploadbox-files::-webkit-scrollbar-thumb:hover {
    background: rgba(99, 102, 241, 0.6) !important;
}

/* Enhanced Modern Grid Layout */
.upload-card-modern .dropzone-form-actions {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 12px !important;
    padding: 0 4px !important;
    margin-bottom: 16px !important;
}

.upload-card-modern .dropzone-form-actions .dropzone-form-action {
    margin-right: 0 !important;
    text-align: center !important;
    padding: 16px 12px !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    min-height: 60px !important;
}

.upload-card-modern .dropzone-form-actions .dropzone-form-action i {
    margin-right: 0 !important;
    margin-bottom: 6px !important;
    font-size: 1.2rem !important;
}

/* Enhanced Modern Button Layout */
.upload-card-modern .dropzone-uploadbox-submit {
    padding: 0 4px 4px 4px !important;
}

.upload-card-modern .dropzone-form-submit {
    padding: 0 4px 4px 4px !important;
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 12px !important;
}

.upload-card-modern .dropzone-form-submit > * {
    margin-right: 0 !important;
}

/* Enhanced Modern Card Stacking */
.upload-card-modern .dropzone-forms {
    margin-top: 16px !important;
}

.upload-card-modern .dropzone-forms .dropzone-form {
    margin-bottom: 0 !important;
    border-radius: 16px !important;
    overflow: hidden !important;
}

.upload-card-modern .dropzone-forms .dropzone-form + .dropzone-form {
    margin-top: 16px !important;
}

/* Enhanced Modern Typography Scale */
.upload-card-modern .dropzone-more-title {
    font-size: 1.1rem !important;
    font-weight: 600 !important;
    color: var(--text-primary) !important;
    margin-bottom: 2px !important;
    line-height: 1.3 !important;
}

.upload-card-modern .dropzone-more-text {
    font-size: 0.85rem !important;
    color: var(--text-muted) !important;
    line-height: 1.4 !important;
    font-weight: 400 !important;
}

.upload-card-modern .dropzone-reset-text {
    font-size: 0.9rem !important;
    color: var(--text-muted) !important;
    margin-top: 4px !important;
    font-weight: 500 !important;
}

/* Enhanced Modern Border Radius System */
.upload-card-modern,
.landing-uploader-card-guest {
    border-radius: 24px !important;
}

.upload-card-modern .dropzone-uploadbox {
    border-radius: 20px !important;
}

.upload-card-modern .dropzone-forms .dropzone-form {
    border-radius: 16px !important;
}

.upload-card-modern .dz-file-preview {
    border-radius: 14px !important;
}

.upload-card-modern .dropzone-form-action {
    border-radius: 12px !important;
}

.upload-card-modern .btn,
.upload-card-modern input,
.upload-card-modern textarea {
    border-radius: 12px !important;
}

.upload-card-modern .form-check-input {
    border-radius: 6px !important;
}

/* Enhanced Modern Shadow System */
.upload-card-modern {
    box-shadow:
        0 25px 50px -12px rgba(0, 0, 0, 0.25),
        0 0 0 1px rgba(99, 102, 241, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.05) !important;
}

.upload-card-modern:hover {
    box-shadow:
        0 32px 64px -12px rgba(0, 0, 0, 0.35),
        0 0 0 1px rgba(99, 102, 241, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

.upload-card-modern .dropzone-uploadbox {
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.2) inset,
        0 4px 20px rgba(99, 102, 241, 0.1),
        0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.upload-card-modern .dz-file-preview {
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.15),
        0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.upload-card-modern .dz-file-preview:hover {
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.2),
        0 2px 8px rgba(99, 102, 241, 0.15) !important;
}

/* Enhanced Modern Responsive Breakpoints */
@media (max-width: 575.98px) {
    .upload-card-modern .dropzone-form-actions {
        grid-template-columns: 1fr !important;
        gap: 8px !important;
    }

    .upload-card-modern .dropzone-form-submit {
        grid-template-columns: 1fr !important;
        gap: 8px !important;
    }

    .upload-card-modern .dropzone-form-action {
        min-height: 50px !important;
        padding: 12px 8px !important;
    }

    .upload-card-modern .dropzone-uploadbox {
        padding: 20px 12px !important;
    }

    .upload-card-modern .dropzone-form-header,
    .upload-card-modern .dropzone-form-body {
        padding-left: 16px !important;
        padding-right: 16px !important;
    }
}

/* Subtle Animations and Micro-interactions */

/* Staggered Animation for File Items */
.upload-card-modern .dz-file-preview {
    animation: slideInFile 0.4s ease-out backwards;
}

.upload-card-modern .dz-file-preview:nth-child(1) { animation-delay: 0.1s; }
.upload-card-modern .dz-file-preview:nth-child(2) { animation-delay: 0.2s; }
.upload-card-modern .dz-file-preview:nth-child(3) { animation-delay: 0.3s; }
.upload-card-modern .dz-file-preview:nth-child(4) { animation-delay: 0.4s; }
.upload-card-modern .dz-file-preview:nth-child(5) { animation-delay: 0.5s; }

@keyframes slideInFile {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Subtle Pulse Animation for Upload Icon */
.upload-card-modern .fa-cloud-arrow-up {
    animation: float 3s ease-in-out infinite, iconGlow 4s ease-in-out infinite;
}

@keyframes iconGlow {
    0%, 100% {
        filter: drop-shadow(0 4px 8px rgba(79, 172, 254, 0.3));
    }
    50% {
        filter: drop-shadow(0 6px 12px rgba(79, 172, 254, 0.5));
    }
}

/* Smooth Form Reveal Animation */
.upload-card-modern .dropzone-forms .dropzone-form.show {
    animation: formSlideIn 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes formSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.98);
        max-height: 0;
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
        max-height: 1000px;
    }
}

/* Subtle Button Press Animation */
.upload-card-modern .btn:active,
.upload-card-modern .dropzone-form-action:active {
    transform: translateY(1px) scale(0.98) !important;
    transition: transform 0.1s ease !important;
}

/* Progress Bar Animation */
.upload-card-modern .dz-progress {
    position: relative !important;
    overflow: hidden !important;
}

.upload-card-modern .dz-progress::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: progressShimmer 2s infinite;
}

@keyframes progressShimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Subtle Hover Lift Animation */
.upload-card-modern .dropzone-more,
.upload-card-modern .dropzone-reset,
.upload-card-modern .drop-down-btn {
    transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.2s ease !important;
}

.upload-card-modern .dropzone-more:hover,
.upload-card-modern .dropzone-reset:hover,
.upload-card-modern .drop-down-btn:hover {
    transform: translateY(-2px) !important;
}

/* Subtle Input Focus Animation */
.upload-card-modern input:focus,
.upload-card-modern textarea:focus {
    animation: inputFocusPulse 0.3s ease-out;
}

@keyframes inputFocusPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

/* Subtle Checkbox Animation */
.upload-card-modern .form-check-input {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.upload-card-modern .form-check-input:checked {
    animation: checkboxPop 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes checkboxPop {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

/* Subtle Tag Animation */
.upload-card-modern .tags-input .tag {
    animation: tagSlideIn 0.3s ease-out;
    transition: all 0.2s ease !important;
}

.upload-card-modern .tags-input .tag:hover {
    transform: translateY(-1px) scale(1.05) !important;
}

@keyframes tagSlideIn {
    from {
        opacity: 0;
        transform: translateX(-10px) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

/* Subtle Loading Animation Enhancement */
.upload-card-modern .btn.loading {
    animation: buttonPulse 1.5s ease-in-out infinite;
}

@keyframes buttonPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

/* Subtle Success Animation */
.transfer-completed-card {
    animation: successSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes successSlideIn {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Subtle Copy Button Animation */
.transfer-completed-card .btn-copy {
    position: relative !important;
    overflow: hidden !important;
}

.transfer-completed-card .btn-copy::after {
    content: 'Copied!' !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) scale(0) !important;
    opacity: 0 !important;
    transition: all 0.3s ease !important;
    font-size: 0.8rem !important;
    font-weight: 600 !important;
}

.transfer-completed-card .btn-copy.copied::after {
    transform: translate(-50%, -50%) scale(1) !important;
    opacity: 1 !important;
}

.transfer-completed-card .btn-copy.copied i {
    opacity: 0 !important;
}

/* Reduce Motion for Accessibility */
@media (prefers-reduced-motion: reduce) {
    .upload-card-modern *,
    .landing-uploader-card-guest * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .upload-card-modern .fa-cloud-arrow-up {
        animation: none !important;
    }
}