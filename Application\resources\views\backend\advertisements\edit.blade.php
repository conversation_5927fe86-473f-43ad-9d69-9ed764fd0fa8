@extends('backend.layouts.form')
@section('title', $advertisement->symbol == 'head_code' ? __('Edit ') . $advertisement->position : __('Edit
    Advertisement | ') . $advertisement->position)
@section('back', route('admin.advertisements.index'))
@section('container', 'container-max-lg')
@section('content')
    @if ($advertisement->size == 'Responsive')
        <div class="alert alert-warning">
            <strong>{{ __('Note') }} : </strong>
            {{ __('Use responsive ad unit to support all devices') }}
        </div>
    @endif
    <form id="vironeer-submited-form" action="{{ route('admin.advertisements.update', $advertisement->id) }}"
        method="POST">
        @csrf
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span><strong>{{ $advertisement->position }}</strong></span>
                <span class="col-4">
                    <input type="checkbox" name="status" data-toggle="toggle"
                        @if ($advertisement->status) checked @endif>
                </span>
            </div>
            <div class="card-body">
                <div class="mb-0">
                    <textarea id="jsContent" name="code" class="form-control" rows="10">{{ $advertisement->code }}</textarea>
                </div>
            </div>
        </div>
    </form>
    @push('styles_libs')
        <link rel="stylesheet" href="{{ asset('assets/vendor/libs/codemirror/codemirror.min.css') }}">
        <link rel="stylesheet" href="{{ asset('assets/vendor/libs/codemirror/monokai.min.css') }}">
    @endpush
    @push('scripts_libs')
        <script src="{{ asset('assets/vendor/libs/codemirror/codemirror.min.js') }}"></script>
        <script src="{{ asset('assets/vendor/libs/codemirror/htmlmixed.js') }}"></script>
        <script src="{{ asset('assets/vendor/libs/codemirror/xml.js') }}"></script>
        <script src="{{ asset('assets/vendor/libs/codemirror/javascript.min.js') }}"></script>
        <script src="{{ asset('assets/vendor/libs/codemirror/sublime.min.js') }}"></script>
    @endpush
@endsection
