!function(b){var c={};function a(d){if(c[d])return c[d].exports;var e=c[d]={i:d,l:!1,exports:{}};return b[d].call(e.exports,e,e.exports,a),e.l=!0,e.exports}a.m=b,a.c=c,a.d=function(b,c,d){a.o(b,c)||Object.defineProperty(b,c,{enumerable:!0,get:d})},a.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},a.t=function(b,c){if(1&c&&(b=a(b)),8&c||4&c&&"object"==typeof b&&b&&b.__esModule)return b;var d=Object.create(null);if(a.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:b}),2&c&&"string"!=typeof b)for(var e in b)a.d(d,e,(function(a){return b[a]}).bind(null,e));return d},a.n=function(c){var b=c&&c.__esModule?function(){return c.default}:function(){return c};return a.d(b,"a",b),b},a.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},a.p="",a(a.s=0)}({"./src/js-loading-overlay.js":function(a,c){function d(d,c){for(var b=0;b<c.length;b++){var a=c[b];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(d,a.key,a)}}var b=function(){var a,b,c;function e(){!function(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}(this,e),this.options={overlayBackgroundColor:"#fffffff2",overlayOpacity:.6,spinnerIcon:"ball-spin-clockwise",spinnerColor:getConfig.primaryColor,spinnerSize:"3x",overlayIDName:"overlay",spinnerIDName:"spinner",offsetY:0,offsetX:0,lockScroll:!1,containerID:null,spinnerZIndex:99999,overlayZIndex:99998},this.stylesheetBaseURL="https://cdn.jsdelivr.net/npm/load-awesome@1.1.0/css/",this.spinner=null,this.spinnerStylesheetURL=null,this.numberOfEmptyDivForSpinner={"ball-8bits":16,"ball-atom":4,"ball-beat":3,"ball-circus":5,"ball-climbing-dot":1,"ball-clip-rotate":1,"ball-clip-rotate-multiple":2,"ball-clip-rotate-pulse":2,"ball-elastic-dots":5,"ball-fall":3,"ball-fussion":4,"ball-grid-beat":9,"ball-grid-pulse":9,"ball-newton-cradle":4,"ball-pulse":3,"ball-pulse-rise":5,"ball-pulse-sync":3,"ball-rotate":1,"ball-running-dots":5,"ball-scale":1,"ball-scale-multiple":3,"ball-scale-pulse":2,"ball-scale-ripple":1,"ball-scale-ripple-multiple":3,"ball-spin":8,"ball-spin-clockwise":8,"ball-spin-clockwise-fade":8,"ball-spin-clockwise-fade-rotating":8,"ball-spin-fade":8,"ball-spin-fade-rotating":8,"ball-spin-rotate":2,"ball-square-clockwise-spin":8,"ball-square-spin":8,"ball-triangle-path":3,"ball-zig-zag":2,"ball-zig-zag-deflect":2,cog:1,"cube-transition":2,fire:3,"line-scale":5,"line-scale-party":5,"line-scale-pulse-out":5,"line-scale-pulse-out-rapid":5,"line-spin-clockwise-fade":8,"line-spin-clockwise-fade-rotating":8,"line-spin-fade":8,"line-spin-fade-rotating":8,pacman:6,"square-jelly-box":2,"square-loader":1,"square-spin":1,timer:1,"triangle-skew-spin":1}}return a=e,b=[{key:"show",value:function(a){this.setOptions(a),this.addSpinnerStylesheet(),this.generateSpinnerElement(),this.options.lockScroll&&(document.body.style.overflow="hidden",document.documentElement.style.overflow="hidden"),this.generateAndAddOverlayElement()}},{key:"hide",value:function(){this.options.lockScroll&&(document.body.style.overflow="",document.documentElement.style.overflow="");var a=document.getElementById("loading-overlay-stylesheet");a&&(a.disabled=!0,a.parentNode.removeChild(a),document.getElementById(this.options.overlayIDName).remove(),document.getElementById(this.options.spinnerIDName).remove())}},{key:"setOptions",value:function(a){if(void 0!==a)for(var b in a)this.options[b]=a[b]}},{key:"generateAndAddOverlayElement",value:function(){var a="50%";0!==this.options.offsetX&&(a="calc(50% + "+this.options.offsetX+")");var b="50%";if(0!==this.options.offsetY&&(b="calc(50% + "+this.options.offsetY+")"),this.options.containerID&&document.body.contains(document.getElementById(this.options.containerID))){var d='<div id="'.concat(this.options.overlayIDName,'" style="display: block !important; position: absolute; top: 0; left: 0; overflow: auto; opacity: ').concat(this.options.overlayOpacity,"; background: ").concat(this.options.overlayBackgroundColor,'; z-index: 50; width: 100%; height: 100%;"></div><div id="').concat(this.options.spinnerIDName,'" style="display: block !important; position: absolute; top: ').concat(b,"; left: ").concat(a,'; -webkit-transform: translate(-50%); -ms-transform: translate(-50%); transform: translate(-50%); z-index: 9999;">').concat(this.spinner,"</div>"),c=document.getElementById(this.options.containerID);c.style.position="relative",c.insertAdjacentHTML("beforeend",d);return}var e='<div id="'.concat(this.options.overlayIDName,'" style="display: block !important; position: fixed; top: 0; left: 0; overflow: auto; opacity: ').concat(this.options.overlayOpacity,"; background: ").concat(this.options.overlayBackgroundColor,"; z-index: ").concat(this.options.overlayZIndex,'; width: 100%; height: 100%;"></div><div id="').concat(this.options.spinnerIDName,'" style="display: block !important; position: fixed; top: ').concat(b,"; left: ").concat(a,"; -webkit-transform: translate(-50%); -ms-transform: translate(-50%); transform: translate(-50%); z-index: ").concat(this.options.spinnerZIndex,';">').concat(this.spinner,"</div>");document.body.insertAdjacentHTML("beforeend",e)}},{key:"generateSpinnerElement",value:function(){var c=this,a=Object.keys(this.numberOfEmptyDivForSpinner).find(function(a){return a===c.options.spinnerIcon}),b=this.generateEmptyDivElement(this.numberOfEmptyDivForSpinner[a]);this.spinner='<div style="color: '.concat(this.options.spinnerColor,'" class="la-').concat(this.options.spinnerIcon," la-").concat(this.options.spinnerSize,'">').concat(b,"</div>")}},{key:"addSpinnerStylesheet",value:function(){this.setSpinnerStylesheetURL();var a=document.createElement("link");a.setAttribute("id","loading-overlay-stylesheet"),a.setAttribute("rel","stylesheet"),a.setAttribute("type","text/css"),a.setAttribute("href",this.spinnerStylesheetURL),document.getElementsByTagName("head")[0].appendChild(a)}},{key:"setSpinnerStylesheetURL",value:function(){this.spinnerStylesheetURL=this.stylesheetBaseURL+this.options.spinnerIcon+".min.css"}},{key:"generateEmptyDivElement",value:function(c){for(var a="",b=1;b<=c;b++)a+="<div></div>";return a}}],d(a.prototype,b),c&&d(a,c),e}();window.JsLoadingOverlay=new b,a.exports=JsLoadingOverlay},0:function(a,c,b){a.exports=b(/*! ./src/js-loading-overlay.js */ "./src/js-loading-overlay.js")}})