<?php
echo "PHP Extensions Check:\n";
echo "====================\n";

// Check GD extension
if (extension_loaded('gd')) {
    echo "✓ GD extension is ENABLED\n";
} else {
    echo "✗ GD extension is DISABLED\n";
}

// Check ZIP extension
if (extension_loaded('zip')) {
    echo "✓ ZIP extension is ENABLED\n";
} else {
    echo "✗ ZIP extension is DISABLED\n";
}

// Check other common Laravel requirements
$required_extensions = ['mbstring', 'openssl', 'pdo', 'tokenizer', 'xml', 'ctype', 'json', 'bcmath'];
echo "\nOther Laravel Requirements:\n";
echo "==========================\n";

foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "✓ $ext extension is ENABLED\n";
    } else {
        echo "✗ $ext extension is DISABLED\n";
    }
}

echo "\nPHP Version: " . phpversion() . "\n";
?> 